#androidx.activity.ComponentActivity.com.example.myapplication.core.common.Resource>com.example.myapplication.core.navigation.MainNavigationScreen0com.example.myapplication.core.navigation.Screenokhttp3.Interceptor:com.example.myapplication.domain.repository.AuthRepositoryFcom.example.myapplication.features.auth.presentation.login.LoginResult-com.example.myapplication.core.common.UiEventEcom.example.myapplication.features.auth.presentation.login.LoginEventandroidx.lifecycle.ViewModel,androidx.lifecycle.ViewModelProvider.FactoryLcom.example.myapplication.features.auth.presentation.register.RegisterResultKcom.example.myapplication.features.auth.presentation.register.RegisterEventkotlin.EnumNcom.example.myapplication.features.profile.domain.repository.ProfileRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     