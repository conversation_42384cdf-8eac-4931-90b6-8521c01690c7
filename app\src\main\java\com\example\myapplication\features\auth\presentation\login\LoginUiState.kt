package com.example.myapplication.features.auth.presentation.login

import com.example.myapplication.core.common.UiEvent
import com.example.myapplication.domain.model.LoginResponse

/**
 * Estado da UI da tela de login
 * 
 * Seguindo SRP: Responsabilidade única de representar o estado da UI de login
 */
data class LoginUiState(
    val usernameOrEmail: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val isPasswordVisible: Boolean = false,
    val loginResult: LoginResult? = null,
    val errorMessage: String? = null
)

/**
 * Resultado do login
 * 
 * Sealed class para representar diferentes estados do resultado
 */
sealed class LoginResult {
    data class Success(val loginResponse: LoginResponse) : LoginResult()
    data class Error(val message: String) : LoginResult()
}

/**
 * Eventos da tela de login
 * 
 * Implementa UiEvent para seguir um padrão consistente
 * Seguindo ISP: Cada evento tem uma responsabilidade específica
 */
sealed class LoginEvent : UiEvent {
    data class UsernameOrEmailChanged(val value: String) : LoginEvent()
    data class PasswordChanged(val value: String) : LoginEvent()
    object TogglePasswordVisibility : LoginEvent()
    object Login : LoginEvent()
    object ClearError : LoginEvent()
}
