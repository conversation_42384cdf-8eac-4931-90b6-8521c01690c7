{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-51:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec212d9d84322c7a85012a35465d0b94\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,298,392,486,579,673,7449", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "192,293,387,481,574,668,764,7545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1710ada1ed9a6075fc2f693eee11811c\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,888,963,1031,1104,1176,1247,1320,1386", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,72,71,70,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,883,958,1026,1099,1171,1242,1315,1381,1497"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,846,922,1007,1098,1175,1249,6864,6942,7017,7090,7165,7233,7306,7378,7550,7623,7689", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,72,71,70,72,65,115", "endOffsets": "841,917,1002,1093,1170,1244,1321,6937,7012,7085,7160,7228,7301,7373,7444,7618,7684,7800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83a88cd926a91d6623e89cd1b3365703\\transformed\\material3-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4051,4134,4216,4308,4388,4470,4568,4662,4755,4850,4934,5030,5126,5223,5331,5411,5503", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4046,4129,4211,4303,4383,4465,4563,4657,4750,4845,4929,5025,5121,5218,5326,5406,5498,5588"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1326,1430,1533,1637,1739,1831,1919,2023,2131,2234,2349,2431,2527,2611,2700,2806,2920,3021,3131,3239,3347,3463,3570,3671,3775,3881,3966,4061,4166,4275,4365,4463,4561,4671,4786,4886,4977,5050,5140,5229,5322,5405,5487,5579,5659,5741,5839,5933,6026,6121,6205,6301,6397,6494,6602,6682,6774", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "1425,1528,1632,1734,1826,1914,2018,2126,2229,2344,2426,2522,2606,2695,2801,2915,3016,3126,3234,3342,3458,3565,3666,3770,3876,3961,4056,4161,4270,4360,4458,4556,4666,4781,4881,4972,5045,5135,5224,5317,5400,5482,5574,5654,5736,5834,5928,6021,6116,6200,6296,6392,6489,6597,6677,6769,6859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb4dfb73410cdab7bbb5bc993c7c0b0\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "7805,7886", "endColumns": "80,76", "endOffsets": "7881,7958"}}]}]}