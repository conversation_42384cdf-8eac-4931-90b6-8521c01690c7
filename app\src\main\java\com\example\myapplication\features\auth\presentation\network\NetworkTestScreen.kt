package com.example.myapplication.features.auth.presentation.network

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.core.constants.AppConstants
import kotlinx.coroutines.launch
import java.net.HttpURLConnection
import java.net.URL

/**
 * Tela para testar conectividade com a API
 * 
 * Seguindo SRP: Responsabilidade única de testar conectividade de rede
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NetworkTestScreen() {
    var testResults by remember { mutableStateOf<List<String>>(emptyList()) }
    var isLoading by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Teste de Conectividade",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        Text(
            text = "URL Configurada: ${AppConstants.Api.BASE_URL}",
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        Button(
            onClick = {
                scope.launch {
                    isLoading = true
                    testResults = testConnectivity()
                    isLoading = false
                }
            },
            enabled = !isLoading,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
            } else {
                Text("Testar Conectividade")
            }
        }
        
        if (testResults.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Resultados dos Testes:",
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    testResults.forEach { result ->
                        Text(
                            text = result,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }
    }
}

private suspend fun testConnectivity(): List<String> {
    val results = mutableListOf<String>()
    val urlsToTest = listOf(
        "http://********:3000/",
        "http://localhost:3000/",
        "http://*************:3000/",
        "http://*************:3000/"
    )
    
    urlsToTest.forEach { baseUrl ->
        try {
            val url = URL("${baseUrl}auth/login")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5000
            connection.readTimeout = 5000
            
            val responseCode = connection.responseCode
            results.add("✅ $baseUrl - Código: $responseCode")
            connection.disconnect()
        } catch (e: Exception) {
            results.add("❌ $baseUrl - Erro: ${e.message}")
        }
    }
    
    return results
}
