package com.example.myapplication.features.profile.domain.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.profile.domain.model.UserProfile

/**
 * Interface do repositório de perfil
 * 
 * Seguindo DIP: Abstração que define contratos para operações de perfil
 * Seguindo ISP: Interface específica para operações de perfil
 */
interface ProfileRepository {
    suspend fun getUserProfile(): Resource<UserProfile>
}
