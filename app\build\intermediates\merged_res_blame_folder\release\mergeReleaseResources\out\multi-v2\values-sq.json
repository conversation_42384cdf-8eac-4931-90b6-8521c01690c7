{"logs": [{"outputFile": "com.example.myapplication.app-mergeReleaseResources-47:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec212d9d84322c7a85012a35465d0b94\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,204,306,404,501,609,720,8484", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "199,301,399,496,604,715,837,8580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb4dfb73410cdab7bbb5bc993c7c0b0\\transformed\\foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8849,8947", "endColumns": "97,98", "endOffsets": "8942,9041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1710ada1ed9a6075fc2f693eee11811c\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1023,1114,1187,1262,1338,1411,1488,1554", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,74,75,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1018,1109,1182,1257,1333,1406,1483,1549,1670"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1020,1119,1221,1317,1398,7827,7919,8009,8096,8187,8260,8335,8411,8585,8662,8728", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,74,75,72,76,65,120", "endOffsets": "932,1015,1114,1216,1312,1393,1486,7914,8004,8091,8182,8255,8330,8406,8479,8657,8723,8844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83a88cd926a91d6623e89cd1b3365703\\transformed\\material3-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,409,527,629,724,836,974,1090,1236,1320,1420,1512,1611,1729,1853,1958,2095,2229,2373,2562,2700,2823,2947,3073,3166,3262,3387,3528,3623,3734,3843,3982,4127,4238,4337,4414,4508,4602,4722,4810,4893,4998,5084,5167,5266,5367,5462,5560,5648,5754,5854,5957,6085,6170,6284", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "169,285,404,522,624,719,831,969,1085,1231,1315,1415,1507,1606,1724,1848,1953,2090,2224,2368,2557,2695,2818,2942,3068,3161,3257,3382,3523,3618,3729,3838,3977,4122,4233,4332,4409,4503,4597,4717,4805,4888,4993,5079,5162,5261,5362,5457,5555,5643,5749,5849,5952,6080,6165,6279,6386"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1491,1610,1726,1845,1963,2065,2160,2272,2410,2526,2672,2756,2856,2948,3047,3165,3289,3394,3531,3665,3809,3998,4136,4259,4383,4509,4602,4698,4823,4964,5059,5170,5279,5418,5563,5674,5773,5850,5944,6038,6158,6246,6329,6434,6520,6603,6702,6803,6898,6996,7084,7190,7290,7393,7521,7606,7720", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,119,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "1605,1721,1840,1958,2060,2155,2267,2405,2521,2667,2751,2851,2943,3042,3160,3284,3389,3526,3660,3804,3993,4131,4254,4378,4504,4597,4693,4818,4959,5054,5165,5274,5413,5558,5669,5768,5845,5939,6033,6153,6241,6324,6429,6515,6598,6697,6798,6893,6991,7079,7185,7285,7388,7516,7601,7715,7822"}}]}]}