package com.example.myapplication.core.security

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class TokenManagerTest {

    private lateinit var context: Context
    private lateinit var tokenManager: TokenManager

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        tokenManager = TokenManager.getInstance(context)
    }

    @After
    fun tearDown() {
        tokenManager.clearTokens()
    }

    @Test
    fun `saveTokens should store tokens securely and update state`() = runTest {
        // Given
        val accessToken = "test_access_token"
        val refreshToken = "test_refresh_token"
        val expiresIn = 3600L
        val userFullName = "Test User"

        // When
        tokenManager.saveTokens(accessToken, refreshToken, expiresIn, userFullName)

        // Then
        assertEquals(accessToken, tokenManager.getAccessToken())
        assertEquals(refreshToken, tokenManager.getRefreshToken())
        assertEquals(userFullName, tokenManager.getCurrentUserName())
        assertTrue(tokenManager.isAuthenticated())
        assertTrue(tokenManager.isLoggedIn.first())
        assertEquals(userFullName, tokenManager.currentUser.first())
    }

    @Test
    fun `getAccessToken should return null when token is expired`() = runTest {
        // Given
        val accessToken = "test_access_token"
        val refreshToken = "test_refresh_token"
        val expiresIn = -1L // Token já expirado
        val userFullName = "Test User"

        // When
        tokenManager.saveTokens(accessToken, refreshToken, expiresIn, userFullName)

        // Then
        assertNull(tokenManager.getAccessToken())
        assertFalse(tokenManager.isAuthenticated())
    }

    @Test
    fun `clearTokens should remove all data and update state`() = runTest {
        // Given
        tokenManager.saveTokens("access", "refresh", 3600L, "User")

        // When
        tokenManager.clearTokens()

        // Then
        assertNull(tokenManager.getAccessToken())
        assertNull(tokenManager.getRefreshToken())
        assertNull(tokenManager.getCurrentUserName())
        assertFalse(tokenManager.isAuthenticated())
        assertFalse(tokenManager.isLoggedIn.first())
        assertNull(tokenManager.currentUser.first())
    }

    @Test
    fun `isTokenExpiringSoon should return true when token expires in less than 5 minutes`() = runTest {
        // Given
        val expiresIn = 4 * 60L // 4 minutos

        // When
        tokenManager.saveTokens("access", "refresh", expiresIn, "User")

        // Then
        assertTrue(tokenManager.isTokenExpiringSoon())
    }

    @Test
    fun `updateAccessToken should update only access token and expiration`() = runTest {
        // Given
        tokenManager.saveTokens("old_access", "refresh", 3600L, "User")
        val newAccessToken = "new_access_token"
        val newExpiresIn = 7200L

        // When
        tokenManager.updateAccessToken(newAccessToken, newExpiresIn)

        // Then
        assertEquals(newAccessToken, tokenManager.getAccessToken())
        assertEquals("refresh", tokenManager.getRefreshToken()) // Deve permanecer inalterado
        assertEquals("User", tokenManager.getCurrentUserName()) // Deve permanecer inalterado
    }
}
