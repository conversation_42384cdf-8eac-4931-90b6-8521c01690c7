1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permission required for network access -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:9:5-32:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:11:9-65
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:12:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:13:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:14:9-41
29        android:networkSecurityConfig="@xml/network_security_config"
29-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:19:9-69
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:15:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:16:9-35
32        android:theme="@style/Theme.MyApplication"
32-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:17:9-51
33        android:usesCleartextTraffic="true" >
33-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:18:9-44
34        <activity
34-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:21:9-31:20
35            android:name="com.example.myapplication.MainActivity"
35-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:22:13-41
36            android:exported="true"
36-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:23:13-36
37            android:label="@string/app_name"
37-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:24:13-45
38            android:theme="@style/Theme.MyApplication" >
38-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:25:13-55
39            <intent-filter>
39-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:26:13-30:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:27:17-69
40-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:27:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:29:17-77
42-->C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\AndroidManifest.xml:29:27-74
43            </intent-filter>
44        </activity>
45
46        <provider
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
47            android:name="androidx.startup.InitializationProvider"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
48            android:authorities="com.example.myapplication.androidx-startup"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
49            android:exported="false" >
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
50            <meta-data
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.emoji2.text.EmojiCompatInitializer"
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
52                android:value="androidx.startup" />
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
54-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
55                android:value="androidx.startup" />
55-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
58                android:value="androidx.startup" />
58-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
59        </provider>
60
61        <receiver
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
62            android:name="androidx.profileinstaller.ProfileInstallReceiver"
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
63            android:directBootAware="false"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
64            android:enabled="true"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
65            android:exported="true"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
66            android:permission="android.permission.DUMP" >
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
68                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
71                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
74                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
77                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
78            </intent-filter>
79        </receiver>
80    </application>
81
82</manifest>
