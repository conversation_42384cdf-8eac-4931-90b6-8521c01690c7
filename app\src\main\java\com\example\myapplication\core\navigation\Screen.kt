package com.example.myapplication.core.navigation

/**
 * Definição das rotas de navegação da aplicação
 *
 * Seguindo OCP: Fácil de estender com novas rotas sem modificar as existentes
 */
sealed class Screen(val route: String) {

    // Auth Feature
    object Login : Screen("login")
    object Register : Screen("register")
    object NetworkTest : Screen("network_test")

    // Main Feature - Agora usa o sistema de navegação com Bottom Navigation
    object Main : Screen("main")

    // Main Navigation Screens (dentro do Main)
    object Dashboard : Screen("dashboard")
    object Obras : Screen("obras")
    object Rankings : Screen("rankings")
    object Profile : Screen("profile")

    // Navegação com argumentos
    object UserProfile : Screen("user/{userId}") {
        fun createRoute(userId: String) = "user/$userId"
    }
}

/**
 * Graphs de navegação para organizar features
 */
object NavigationGraph {
    const val AUTH = "auth_graph"
    const val MAIN = "main_graph"
    const val ROOT = "root_graph"
}
