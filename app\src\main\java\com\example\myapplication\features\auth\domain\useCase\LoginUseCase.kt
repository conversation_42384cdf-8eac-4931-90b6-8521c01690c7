package com.example.myapplication.domain.useCase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.constants.AppConstants
import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.repository.AuthRepository

/**
 * Caso de uso para login
 * 
 * Seguindo SRP: Responsabilidade única de executar a lógica de login
 * Seguindo DIP: Depende da abstração AuthRepository
 */
class LoginUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(usernameOrEmail: String, password: String): Resource<LoginResponse> {
        when {
            usernameOrEmail.isBlank() -> return Resource.Error("Nome de usuário ou email é obrigatório")
            password.isBlank() -> return Resource.Error("Senha é obrigatória")
            password.length < AppConstants.Validation.MIN_PASSWORD_LENGTH -> 
                return Resource.Error("Senha deve ter pelo menos ${AppConstants.Validation.MIN_PASSWORD_LENGTH} caracteres")
        }

        val loginRequest = LoginRequest(
            usernameOrEmail = usernameOrEmail.trim(),
            password = password
        )
        return authRepository.login(loginRequest)
    }
}
