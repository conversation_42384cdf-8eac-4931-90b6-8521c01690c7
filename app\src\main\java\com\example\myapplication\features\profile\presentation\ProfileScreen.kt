package com.example.myapplication.features.profile.presentation

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.core.di.AppContainer
import com.example.myapplication.features.profile.domain.model.ReadingStats
import com.example.myapplication.features.profile.domain.model.UserProfile
import com.example.myapplication.features.profile.domain.model.UserStats
import com.example.myapplication.ui.theme.MyApplicationTheme

/**
 * Tela de Profile - Perfil do usuário e configurações
 * 
 * Seguindo SRP: Responsabilidade única de exibir e gerenciar perfil
 * Seguindo OCP: Extensível para novas configurações e funcionalidades
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    userFullName: String = "João Silva",
    onLogout: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val appContainer = remember { AppContainer.getInstance(context) }
    val viewModel: ProfileViewModel = viewModel(
        factory = ProfileViewModelFactory(appContainer)
    )
    val uiState by viewModel.uiState.collectAsState()

    // Mostrar loading ou erro se necessário
    when {
        uiState.isLoading -> {
            LoadingScreen(modifier = modifier)
            return
        }
        uiState.error != null -> {
            ErrorScreen(
                error = uiState.error ?: "Erro desconhecido",
                onRetry = { viewModel.loadUserProfile() },
                modifier = modifier
            )
            return
        }
    }

    val userProfile = uiState.userProfile
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Header do perfil
            ProfileHeader(userProfile = userProfile)
        }

        item {
            // Estatísticas do usuário
            UserStatsSection(userStats = userProfile?.stats)
        }
        
        item {
            // Configurações da conta
            AccountSettingsSection()
        }
        
        item {
            // Configurações do app
            AppSettingsSection()
        }
        
        item {
            // Suporte e informações
            SupportSection()
        }
        
        item {
            // Logout
            LogoutSection(onLogout = onLogout)
        }
    }
}

@Composable
private fun ProfileHeader(userProfile: UserProfile?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Avatar placeholder
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Card(
                    modifier = Modifier.fillMaxSize(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Avatar",
                            modifier = Modifier.size(40.dp),
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = userProfile?.fullName ?: "Carregando...",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )

            Text(
                text = "@${userProfile?.username ?: ""}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            OutlinedButton(
                onClick = { /* TODO: Implementar edição de perfil */ },
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Edit,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Editar Perfil")
            }
        }
    }
}

@Composable
private fun UserStatsSection(userStats: UserStats?) {
    Text(
        text = "Suas Estatísticas",
        fontSize = 20.sp,
        fontWeight = FontWeight.SemiBold,
        modifier = Modifier.padding(bottom = 8.dp)
    )
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        StatCard(
            title = "Obras",
            value = "${userStats?.readingStats?.completed ?: 0}",
            subtitle = "Concluídas",
            icon = Icons.Default.Build,
            modifier = Modifier.weight(1f)
        )
        StatCard(
            title = "Capítulos",
            value = "${userStats?.totalChaptersRead ?: 0}",
            subtitle = "Lidos",
            icon = Icons.Default.MenuBook,
            modifier = Modifier.weight(1f)
        )
    }
    
    Spacer(modifier = Modifier.height(8.dp))
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        StatCard(
            title = "Reviews",
            value = "${userStats?.totalReviews ?: 0}",
            subtitle = "Escritas",
            icon = Icons.Default.RateReview,
            modifier = Modifier.weight(1f)
        )
        StatCard(
            title = "Avaliação",
            value = String.format("%.1f", userStats?.averageRating ?: 0.0),
            subtitle = "Média",
            icon = Icons.Default.Star,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun StatCard(
    title: String,
    value: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = value,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = title,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = subtitle,
                fontSize = 10.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun AccountSettingsSection() {
    SettingsSection(
        title = "Conta",
        items = listOf(
            SettingsItem(
                icon = Icons.Default.Person,
                title = "Informações Pessoais",
                subtitle = "Nome, email, telefone",
                onClick = { /* TODO: Implementar */ }
            ),
            SettingsItem(
                icon = Icons.Default.Lock,
                title = "Segurança",
                subtitle = "Senha, autenticação",
                onClick = { /* TODO: Implementar */ }
            ),
            SettingsItem(
                icon = Icons.Default.Business,
                title = "Empresa",
                subtitle = "Dados da empresa",
                onClick = { /* TODO: Implementar */ }
            )
        )
    )
}

@Composable
private fun AppSettingsSection() {
    SettingsSection(
        title = "Aplicativo",
        items = listOf(
            SettingsItem(
                icon = Icons.Default.Notifications,
                title = "Notificações",
                subtitle = "Gerenciar notificações",
                onClick = { /* TODO: Implementar */ }
            ),
            SettingsItem(
                icon = Icons.Default.Palette,
                title = "Tema",
                subtitle = "Aparência do app",
                onClick = { /* TODO: Implementar */ }
            ),
            SettingsItem(
                icon = Icons.Default.Language,
                title = "Idioma",
                subtitle = "Português (Brasil)",
                onClick = { /* TODO: Implementar */ }
            )
        )
    )
}

@Composable
private fun SupportSection() {
    SettingsSection(
        title = "Suporte",
        items = listOf(
            SettingsItem(
                icon = Icons.Default.Help,
                title = "Central de Ajuda",
                subtitle = "FAQ e tutoriais",
                onClick = { /* TODO: Implementar */ }
            ),
            SettingsItem(
                icon = Icons.Default.ContactSupport,
                title = "Contato",
                subtitle = "Fale conosco",
                onClick = { /* TODO: Implementar */ }
            ),
            SettingsItem(
                icon = Icons.Default.Info,
                title = "Sobre",
                subtitle = "Versão 1.0.0",
                onClick = { /* TODO: Implementar */ }
            )
        )
    )
}

@Composable
private fun SettingsSection(
    title: String,
    items: List<SettingsItem>
) {
    Text(
        text = title,
        fontSize = 20.sp,
        fontWeight = FontWeight.SemiBold,
        modifier = Modifier.padding(bottom = 8.dp)
    )
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            items.forEachIndexed { index, item ->
                SettingsItemRow(item = item)
                if (index < items.size - 1) {
                    Divider(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        color = MaterialTheme.colorScheme.outlineVariant
                    )
                }
            }
        }
    }
}

@Composable
private fun SettingsItemRow(item: SettingsItem) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = item.icon,
            contentDescription = item.title,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = item.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = item.subtitle,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = "Ir para ${item.title}",
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun LogoutSection(onLogout: () -> Unit) {
    OutlinedButton(
        onClick = onLogout,
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = MaterialTheme.colorScheme.error
        )
    ) {
        Icon(
            imageVector = Icons.Default.ExitToApp,
            contentDescription = "Sair",
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text("Sair da Conta")
    }
}

// Data class
data class SettingsItem(
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val title: String,
    val subtitle: String,
    val onClick: () -> Unit
)

@Composable
private fun LoadingScreen(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text("Carregando perfil...")
        }
    }
}

@Composable
private fun ErrorScreen(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = "Erro",
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Erro ao carregar perfil",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = error,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onRetry) {
                Text("Tentar Novamente")
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProfileScreenPreview() {
    MyApplicationTheme {
        ProfileScreen(
            userFullName = "João Silva"
        )
    }
}
