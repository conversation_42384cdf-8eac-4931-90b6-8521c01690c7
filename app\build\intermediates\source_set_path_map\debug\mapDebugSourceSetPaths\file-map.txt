com.example.myapplication.app-lifecycle-viewmodel-savedstate-2.8.3-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\044991b15de6ce7ecdacb990fc8f570d\transformed\lifecycle-viewmodel-savedstate-2.8.3\res
com.example.myapplication.app-ui-test-manifest-1.7.0-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d955b063841d8ced20f95707b5602e\transformed\ui-test-manifest-1.7.0\res
com.example.myapplication.app-customview-poolingcontainer-1.0.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08b310b3b29ca9937f519a72883a051a\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapplication.app-security-crypto-1.1.0-alpha06-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c712c57bc38fd0b886fae7d524dbff7\transformed\security-crypto-1.1.0-alpha06\res
com.example.myapplication.app-core-runtime-2.2.0-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14a5caf0593a3355120e065e7a3b6ade\transformed\core-runtime-2.2.0\res
com.example.myapplication.app-ui-release-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1710ada1ed9a6075fc2f693eee11811c\transformed\ui-release\res
com.example.myapplication.app-foundation-layout-release-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1956d02c6b37d95d4bd1c4daf95f3448\transformed\foundation-layout-release\res
com.example.myapplication.app-animation-release-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1e0b35d6dd205ea3e648b0c7aac6e50e\transformed\animation-release\res
com.example.myapplication.app-navigation-runtime-ktx-2.7.6-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\209c4b978e93cc99899bc77bab746bb9\transformed\navigation-runtime-ktx-2.7.6\res
com.example.myapplication.app-lifecycle-viewmodel-compose-release-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2252d8de22fed64399f186c7631c38c7\transformed\lifecycle-viewmodel-compose-release\res
com.example.myapplication.app-savedstate-ktx-1.2.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22720686edf6cf305a2cca66b169d3af\transformed\savedstate-ktx-1.2.1\res
com.example.myapplication.app-ui-geometry-release-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\285a290ca101940d24d32530105246a2\transformed\ui-geometry-release\res
com.example.myapplication.app-lifecycle-viewmodel-ktx-2.8.3-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f89fc34ed29095fd48e39470f5f0217\transformed\lifecycle-viewmodel-ktx-2.8.3\res
com.example.myapplication.app-activity-compose-1.8.2-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bc801f41b2c71c928c904bbf0610057\transformed\activity-compose-1.8.2\res
com.example.myapplication.app-ui-tooling-preview-release-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d8b0ee944a0be9c495d8186473cc448\transformed\ui-tooling-preview-release\res
com.example.myapplication.app-lifecycle-livedata-core-2.8.3-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d7411603ce8ab3cb365e4bd34393b6\transformed\lifecycle-livedata-core-2.8.3\res
com.example.myapplication.app-navigation-common-ktx-2.7.6-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42b806c24630b94cb711188da9934778\transformed\navigation-common-ktx-2.7.6\res
com.example.myapplication.app-material-icons-extended-release-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42d21c9da061d7f926279ba835188a77\transformed\material-icons-extended-release\res
com.example.myapplication.app-navigation-runtime-2.7.6-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44b61af9eff353bc80aaad72695beddb\transformed\navigation-runtime-2.7.6\res
com.example.myapplication.app-foundation-release-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4eb4dfb73410cdab7bbb5bc993c7c0b0\transformed\foundation-release\res
com.example.myapplication.app-startup-runtime-1.1.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57f253c10c64e66cc7f038934118cb95\transformed\startup-runtime-1.1.1\res
com.example.myapplication.app-runtime-release-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64e6a031104d088893c6cb03e8e19935\transformed\runtime-release\res
com.example.myapplication.app-animation-core-release-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6865231d74ee434f90da421393f0fca0\transformed\animation-core-release\res
com.example.myapplication.app-graphics-path-1.0.1-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a3ba5929a4b5338f65fd67c2d885d9b\transformed\graphics-path-1.0.1\res
com.example.myapplication.app-navigation-common-2.7.6-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7482a8edd3834ff73380928760fb7153\transformed\navigation-common-2.7.6\res
com.example.myapplication.app-lifecycle-runtime-ktx-release-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81568d19ac26e13f2d97dcea0d4cf72a\transformed\lifecycle-runtime-ktx-release\res
com.example.myapplication.app-material3-release-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83a88cd926a91d6623e89cd1b3365703\transformed\material3-release\res
com.example.myapplication.app-ui-tooling-data-release-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\876ce60daece3cd1213db0eb313071ae\transformed\ui-tooling-data-release\res
com.example.myapplication.app-core-ktx-1.13.1-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d446eb8df5ef11e751e67ec1a30d7be\transformed\core-ktx-1.13.1\res
com.example.myapplication.app-profileinstaller-1.3.1-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ee5bae3dce5a7c34c6adc14c2ee5282\transformed\profileinstaller-1.3.1\res
com.example.myapplication.app-ui-util-release-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\997185d02878fb760fa0a69f2e61556c\transformed\ui-util-release\res
com.example.myapplication.app-ui-tooling-release-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bbd1320823815804c41acf77e8c3cba\transformed\ui-tooling-release\res
com.example.myapplication.app-lifecycle-runtime-compose-release-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e6ca63e7f9870bbd15ad4f23237bb44\transformed\lifecycle-runtime-compose-release\res
com.example.myapplication.app-navigation-compose-2.7.6-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f026c3490516a5b729092c9f4f253c6\transformed\navigation-compose-2.7.6\res
com.example.myapplication.app-material-ripple-release-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0acceb8cc37f9560d8ade69349708c5\transformed\material-ripple-release\res
com.example.myapplication.app-lifecycle-process-2.8.3-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2f3902c5a7b65349e24f9cb6b92ec03\transformed\lifecycle-process-2.8.3\res
com.example.myapplication.app-lifecycle-viewmodel-release-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3736123c013e6a85fae0b04ea9b562f\transformed\lifecycle-viewmodel-release\res
com.example.myapplication.app-material-icons-core-release-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5ec4f76c471a13ab815affbf7b0cc9f\transformed\material-icons-core-release\res
com.example.myapplication.app-lifecycle-runtime-release-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aea8ae5a9f5eb68bae8aad8913142fe8\transformed\lifecycle-runtime-release\res
com.example.myapplication.app-ui-unit-release-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5b3788d08449f42416531f4a39492da\transformed\ui-unit-release\res
com.example.myapplication.app-ui-graphics-release-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4b457b1c931c41cdb04e0268ca0d26e\transformed\ui-graphics-release\res
com.example.myapplication.app-annotation-experimental-1.4.0-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8693ff8d58649a152b97f29130974f8\transformed\annotation-experimental-1.4.0\res
com.example.myapplication.app-emoji2-1.3.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb74523412b64831387e64018ef95b46\transformed\emoji2-1.3.0\res
com.example.myapplication.app-activity-ktx-1.8.2-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cbf6fd7861628fbd906be5c578d82296\transformed\activity-ktx-1.8.2\res
com.example.myapplication.app-runtime-saveable-release-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d74c1348bba3c8fe23cebf70ed01e9ae\transformed\runtime-saveable-release\res
com.example.myapplication.app-material-release-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d830329904c4c8f4e5ad7c3e23d6def7\transformed\material-release\res
com.example.myapplication.app-savedstate-1.2.1-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e522afad30dd90c13339f2b23c04ebaf\transformed\savedstate-1.2.1\res
com.example.myapplication.app-activity-1.8.2-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e78ab23a12514ed875ac1f1d721cce36\transformed\activity-1.8.2\res
com.example.myapplication.app-core-1.13.1-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec212d9d84322c7a85012a35465d0b94\transformed\core-1.13.1\res
com.example.myapplication.app-ui-text-release-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eed1c1b7854d8a148c21c870c857f498\transformed\ui-text-release\res
com.example.myapplication.app-pngs-50 C:\Users\<USER>\Documents\mobile\MyApplication\app\build\generated\res\pngs\debug
com.example.myapplication.app-resValues-51 C:\Users\<USER>\Documents\mobile\MyApplication\app\build\generated\res\resValues\debug
com.example.myapplication.app-packageDebugResources-52 C:\Users\<USER>\Documents\mobile\MyApplication\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapplication.app-packageDebugResources-53 C:\Users\<USER>\Documents\mobile\MyApplication\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapplication.app-debug-54 C:\Users\<USER>\Documents\mobile\MyApplication\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapplication.app-debug-55 C:\Users\<USER>\Documents\mobile\MyApplication\app\src\debug\res
com.example.myapplication.app-main-56 C:\Users\<USER>\Documents\mobile\MyApplication\app\src\main\res
