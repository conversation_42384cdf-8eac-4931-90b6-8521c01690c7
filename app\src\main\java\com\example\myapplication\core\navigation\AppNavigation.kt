package com.example.myapplication.core.navigation

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.ui.graphics.Color
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.core.di.AppContainer
import com.example.myapplication.features.auth.presentation.login.LoginResult
import com.example.myapplication.features.auth.presentation.login.LoginScreen
import com.example.myapplication.features.auth.presentation.login.LoginViewModel
import com.example.myapplication.features.auth.presentation.login.LoginViewModelFactory
import com.example.myapplication.features.auth.presentation.register.RegisterResult
import com.example.myapplication.features.auth.presentation.register.RegisterScreen
import com.example.myapplication.features.auth.presentation.register.RegisterViewModel
import com.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory
import com.example.myapplication.features.auth.presentation.network.NetworkTestScreen
import com.example.myapplication.features.main.presentation.MainNavigationScreen

/**
 * Componente de navegação da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar navegação
 * Seguindo OCP: Extensível para novos fluxos de navegação
 */
@Composable
fun AppNavigation(
    onShowToast: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    // DEBUG: Vamos simplificar temporariamente para identificar o problema
    val context = LocalContext.current

    // Primeiro, vamos tentar sem o TokenManager para ver se o problema está aí
    val currentScreen = remember { mutableStateOf<Screen>(Screen.Login) }

    // DEBUG: Adicionar logs para debug
    LaunchedEffect(Unit) {
        println("DEBUG: AppNavigation iniciado")
    }

    // Temporariamente vamos forçar a tela de login sem verificar tokens
    val isLoggedIn = false // Forçar false para debug
    val currentUser = remember { mutableStateOf("Usuário Debug") }

    when (currentScreen.value) {
        Screen.Login -> {
            // DEBUG: Vamos mostrar uma tela simples primeiro
            println("DEBUG: Mostrando tela de login")

            Column(
                modifier = modifier
                    .fillMaxSize()
                    .background(Color.White)
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "DEBUG: Tela de Login",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(32.dp))

                Button(
                    onClick = {
                        onShowToast("DEBUG: Botão funcionando!")
                        currentScreen.value = Screen.Main
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("DEBUG: Ir para Main")
                }

                Spacer(modifier = Modifier.height(16.dp))

                OutlinedButton(
                    onClick = {
                        onShowToast("DEBUG: Teste de rede!")
                        currentScreen.value = Screen.NetworkTest
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("DEBUG: Teste de Rede")
                }
            }
        }        Screen.Register -> {
            val appContainer = remember { AppContainer.getInstance(context) }
            val registerViewModel: RegisterViewModel = viewModel(
                factory = RegisterViewModelFactory(appContainer)
            )

            RegisterScreen(
                viewModel = registerViewModel,
                onRegisterSuccess = { result: RegisterResult.Success ->
                    onShowToast("Conta criada com sucesso! Bem-vindo, ${result.registerResponse.user.fullName}")
                    currentUser.value = result.registerResponse.user.fullName
                    currentScreen.value = Screen.Main
                },
                onNavigateToLogin = {
                    currentScreen.value = Screen.Login
                },
                modifier = modifier
            )
        }
        Screen.NetworkTest -> {
            NetworkTestScreen()
        }
        Screen.Main -> {
            MainNavigationScreen(
                userFullName = currentUser.value,
                onLogout = {
                    // DEBUG: Simplificar logout por enquanto
                    currentUser.value = "Usuário"
                    currentScreen.value = Screen.Login
                },
                modifier = modifier
            )
        }
        else -> {
            // Fallback para tela de login
            currentScreen.value = Screen.Login
        }
    }
}
