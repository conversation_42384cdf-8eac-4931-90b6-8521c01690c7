package com.example.myapplication.core.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.features.auth.presentation.login.LoginResult
import com.example.myapplication.features.auth.presentation.login.LoginScreen
import com.example.myapplication.features.auth.presentation.login.LoginViewModel
import com.example.myapplication.features.auth.presentation.login.LoginViewModelFactory
import com.example.myapplication.features.auth.presentation.register.RegisterResult
import com.example.myapplication.features.auth.presentation.register.RegisterScreen
import com.example.myapplication.features.auth.presentation.register.RegisterViewModel
import com.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory
import com.example.myapplication.features.auth.presentation.network.NetworkTestScreen
import com.example.myapplication.features.main.presentation.MainNavigationScreen

/**
 * Componente de navegação da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar navegação
 * Seguindo OCP: Extensível para novos fluxos de navegação
 */
@Composable
fun AppNavigation(
    onShowToast: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val currentScreen = remember { mutableStateOf<Screen>(Screen.Login) }
    val isLoggedIn = remember { mutableStateOf(false) }
    val currentUser = remember { mutableStateOf<String?>(null) }

    when (currentScreen.value) {
        Screen.Login -> {
            if (!isLoggedIn.value) {
                val loginViewModel: LoginViewModel = viewModel(
                    factory = LoginViewModelFactory()
                )

                LoginScreen(
                    viewModel = loginViewModel,
                    onLoginSuccess = { result: LoginResult.Success ->
                        onShowToast("Login realizado com sucesso! Bem-vindo, ${result.loginResponse.user.fullName}")
                        currentUser.value = result.loginResponse.user.fullName
                        isLoggedIn.value = true
                        currentScreen.value = Screen.Main
                    },
                    onShowNetworkTest = {
                        currentScreen.value = Screen.NetworkTest
                    },
                    onNavigateToRegister = {
                        currentScreen.value = Screen.Register
                    },
                    modifier = modifier
                )
            } else {
                currentScreen.value = Screen.Main
            }
        }

        Screen.Register -> {
            val registerViewModel: RegisterViewModel = viewModel(
                factory = RegisterViewModelFactory()
            )

            RegisterScreen(
                viewModel = registerViewModel,
                onRegisterSuccess = { result: RegisterResult.Success ->
                    onShowToast("Conta criada com sucesso! Bem-vindo, ${result.registerResponse.user.fullName}")
                    currentUser.value = result.registerResponse.user.fullName
                    isLoggedIn.value = true
                    currentScreen.value = Screen.Main
                },
                onNavigateToLogin = {
                    currentScreen.value = Screen.Login
                },
                modifier = modifier
            )
        }

        Screen.NetworkTest -> {
            NetworkTestScreen()
        }

        Screen.Main -> {
            MainNavigationScreen(
                userFullName = currentUser.value ?: "Usuário",
                onLogout = {
                    isLoggedIn.value = false
                    currentUser.value = null
                    currentScreen.value = Screen.Login
                },
                modifier = modifier
            )
        }

        else -> {
            // Fallback para tela de login
            currentScreen.value = Screen.Login
        }
    }
}
