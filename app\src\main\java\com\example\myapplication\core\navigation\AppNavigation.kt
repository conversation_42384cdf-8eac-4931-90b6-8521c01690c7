package com.example.myapplication.core.navigation

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.core.di.AppContainer
import com.example.myapplication.features.auth.presentation.login.LoginResult
import com.example.myapplication.features.auth.presentation.login.LoginScreen
import com.example.myapplication.features.auth.presentation.login.LoginViewModel
import com.example.myapplication.features.auth.presentation.login.LoginViewModelFactory
import com.example.myapplication.features.auth.presentation.register.RegisterResult
import com.example.myapplication.features.auth.presentation.register.RegisterScreen
import com.example.myapplication.features.auth.presentation.register.RegisterViewModel
import com.example.myapplication.features.auth.presentation.register.RegisterViewModelFactory
import com.example.myapplication.features.auth.presentation.network.NetworkTestScreen
import com.example.myapplication.features.main.presentation.MainNavigationScreen

/**
 * Componente de navegação da aplicação
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar navegação
 * Seguindo OCP: Extensível para novos fluxos de navegação
 */
@Composable
fun AppNavigation(
    onShowToast: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    // DEBUG: Versão super simplificada para identificar o problema
    println("DEBUG: AppNavigation iniciando")

    // Tela de teste simples
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "🚀 APP FUNCIONANDO! 🚀",
            style = MaterialTheme.typography.headlineLarge,
            color = Color.Blue
        )

        Spacer(modifier = Modifier.height(32.dp))

        Button(
            onClick = {
                onShowToast("Botão funcionando!")
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Teste de Botão")
        }
    }
}
