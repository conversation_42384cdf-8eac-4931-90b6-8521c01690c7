package com.example.myapplication.features.auth.presentation.register

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.security.TokenManager
import com.example.myapplication.domain.useCase.RegisterUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel para a tela de registro
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar o estado da tela de registro
 * Seguindo DIP: Depende da abstração RegisterUseCase, não da implementação
 */
class RegisterViewModel(
    private val registerUseCase: RegisterUseCase,
    private val tokenManager: TokenManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(RegisterUiState())
    val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()

    fun onEvent(event: RegisterEvent) {
        when (event) {
            is RegisterEvent.UsernameChanged -> {
                _uiState.value = _uiState.value.copy(
                    username = event.value,
                    errorMessage = null
                )
            }
            is RegisterEvent.EmailChanged -> {
                _uiState.value = _uiState.value.copy(
                    email = event.value,
                    errorMessage = null
                )
            }
            is RegisterEvent.PasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    password = event.value,
                    errorMessage = null
                )
            }
            is RegisterEvent.FullNameChanged -> {
                _uiState.value = _uiState.value.copy(
                    fullName = event.value,
                    errorMessage = null
                )
            }
            is RegisterEvent.TogglePasswordVisibility -> {
                _uiState.value = _uiState.value.copy(
                    isPasswordVisible = !_uiState.value.isPasswordVisible
                )
            }
            is RegisterEvent.Register -> {
                performRegister()
            }
            is RegisterEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(
                    errorMessage = null,
                    registerResult = null
                )
            }
            is RegisterEvent.NavigateToLogin -> {
                // Evento será tratado na UI
            }
        }
    }

    private fun performRegister() {
        val currentState = _uiState.value
        
        if (currentState.isLoading) return
        
        _uiState.value = currentState.copy(
            isLoading = true,
            errorMessage = null,
            registerResult = null
        )

        viewModelScope.launch {
            val result = registerUseCase(
                username = currentState.username,
                email = currentState.email,
                password = currentState.password,
                fullName = currentState.fullName.takeIf { it.isNotBlank() }
            )

            when (result) {
                is Resource.Success -> {
                    val registerResponse = result.data!!
                    
                    // Salva tokens de forma segura
                    tokenManager.saveTokens(
                        accessToken = registerResponse.accessToken,
                        refreshToken = registerResponse.refreshToken,
                        expiresIn = registerResponse.expiresIn,
                        userFullName = registerResponse.user.fullName
                    )
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        registerResult = RegisterResult.Success(registerResponse)
                    )
                }
                is Resource.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = result.message,
                        registerResult = RegisterResult.Error(result.message ?: "Erro desconhecido")
                    )
                }
                is Resource.Loading -> {
                    // Estado já definido acima
                }
            }
        }
    }
}
