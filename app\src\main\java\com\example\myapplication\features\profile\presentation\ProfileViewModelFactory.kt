package com.example.myapplication.features.profile.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.core.di.AppContainer
import com.example.myapplication.features.profile.domain.usecase.GetUserProfileUseCase

/**
 * Factory para criar ProfileViewModel
 * 
 * Seguindo SRP: Responsabilidade única de criar ProfileViewModel
 * Seguindo DIP: Injeta dependências através do AppContainer
 */
class ProfileViewModelFactory(
    private val getUserProfileUseCase: GetUserProfileUseCase = AppContainer.getUserProfileUseCase
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ProfileViewModel::class.java)) {
            return ProfileViewModel(getUserProfileUseCase) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
