package com.example.myapplication.features.profile.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.core.di.AppContainer

/**
 * Factory para criar ProfileViewModel
 * 
 * Seguindo SRP: Responsabilidade única de criar ProfileViewModel
 * Seguindo DIP: Injeta dependências através do AppContainer
 */
class ProfileViewModelFactory(
    private val appContainer: AppContainer
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ProfileViewModel::class.java)) {
            return ProfileViewModel(appContainer.getUserProfileUseCase) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
