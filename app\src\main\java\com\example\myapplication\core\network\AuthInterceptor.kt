package com.example.myapplication.core.network

import com.example.myapplication.core.security.TokenManager
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response

/**
 * Interceptor para adicionar automaticamente o token de autorização nas requisições
 * 
 * Funcionalidades:
 * - Adiciona Bearer token automaticamente nas requisições autenticadas
 * - Intercepta requisições que precisam de autenticação
 * - Permite renovação de token quando necessário
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar autenticação nas requisições
 * Seguindo OCP: Extensível para diferentes tipos de autenticação
 */
class AuthInterceptor(
    private val tokenManager: TokenManager
) : Interceptor {

    companion object {
        // Endpoints que não precisam de autenticação
        private val OPEN_ENDPOINTS = listOf(
            "/auth/login",
            "/auth/register",
            "/auth/refresh"
        )
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestPath = originalRequest.url.encodedPath
        
        // Verifica se o endpoint precisa de autenticação
        val needsAuth = OPEN_ENDPOINTS.none { requestPath.contains(it) }
        
        if (!needsAuth) {
            // Processa requisição sem token
            return chain.proceed(originalRequest)
        }

        // Obtém token de acesso
        val accessToken = tokenManager.getAccessToken()
        
        if (accessToken == null) {
            // Token não disponível ou expirado - deixa a requisição prosseguir
            // O repository/use case deve tratar o erro 401
            return chain.proceed(originalRequest)
        }

        // Adiciona token de autorização
        val authenticatedRequest = originalRequest.newBuilder()
            .addHeader("Authorization", "Bearer $accessToken")
            .build()

        val response = chain.proceed(authenticatedRequest)

        // Se receber 401 (Unauthorized), pode indicar token expirado
        if (response.code == 401) {
            response.close()
            
            // Tenta renovar token se disponível
            val refreshToken = tokenManager.getRefreshToken()
            if (refreshToken != null) {
                // TODO: Implementar renovação automática de token aqui
                // Por enquanto, limpa tokens para forçar novo login
                tokenManager.clearTokens()
            }
            
            // Retenta requisição original sem token
            return chain.proceed(originalRequest)
        }

        return response
    }
}
