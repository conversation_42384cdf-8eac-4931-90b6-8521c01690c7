package com.example.myapplication.features.profile.data.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.data.remote.api.ApiService
import com.example.myapplication.features.profile.domain.model.ReadingStats
import com.example.myapplication.features.profile.domain.model.UserProfile
import com.example.myapplication.features.profile.domain.model.UserStats
import com.example.myapplication.features.profile.domain.repository.ProfileRepository
import retrofit2.HttpException
import java.io.IOException

/**
 * Implementação do repositório de perfil
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar dados de perfil
 * Seguindo DIP: Implementa a abstração ProfileRepository
 */
class ProfileRepositoryImpl(
    private val apiService: ApiService
) : ProfileRepository {

    override suspend fun getUserProfile(): Resource<UserProfile> {
        return try {
            val response = apiService.getProfile()
            
            if (response.isSuccessful) {
                val profileDto = response.body()
                if (profileDto != null) {
                    val userProfile = UserProfile(
                        id = profileDto.id,
                        username = profileDto.username,
                        email = profileDto.email,
                        fullName = profileDto.fullName,
                        avatar = profileDto.avatar,
                        isActive = profileDto.isActive,
                        role = profileDto.role,
                        createdAt = profileDto.createdAt,
                        updatedAt = profileDto.updatedAt,
                        stats = UserStats(
                            readingStats = ReadingStats(
                                reading = profileDto.stats.readingStats.reading,
                                completed = profileDto.stats.readingStats.completed,
                                dropped = profileDto.stats.readingStats.dropped,
                                planToRead = profileDto.stats.readingStats.planToRead,
                                onHold = profileDto.stats.readingStats.onHold,
                                totalWorks = profileDto.stats.readingStats.totalWorks
                            ),
                            totalChaptersRead = profileDto.stats.totalChaptersRead,
                            totalLists = profileDto.stats.totalLists,
                            totalPublicLists = profileDto.stats.totalPublicLists,
                            totalRankings = profileDto.stats.totalRankings,
                            totalPublicRankings = profileDto.stats.totalPublicRankings,
                            totalReviews = profileDto.stats.totalReviews,
                            totalPublicReviews = profileDto.stats.totalPublicReviews,
                            averageRating = profileDto.stats.averageRating,
                            totalReviewLikes = profileDto.stats.totalReviewLikes,
                            averageReadingTime = profileDto.stats.averageReadingTime,
                            lastReadingActivity = profileDto.stats.lastReadingActivity,
                            favoriteGenre = profileDto.stats.favoriteGenre
                        )
                    )
                    Resource.Success(userProfile)
                } else {
                    Resource.Error("Resposta vazia do servidor")
                }
            } else {
                Resource.Error("Erro ao buscar perfil: ${response.code()}")
            }
            
        } catch (e: HttpException) {
            Resource.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            Resource.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            Resource.Error("Erro inesperado: ${e.message}")
        }
    }
}
