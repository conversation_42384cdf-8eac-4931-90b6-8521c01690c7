package com.example.myapplication.features.auth.presentation.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.core.di.AppContainer

/**
 * Factory para criar instâncias do LoginViewModel
 * 
 * Seguindo DIP: Usa o AppContainer para resolver dependências
 * Seguindo SRP: Responsabilidade única de criar ViewModels de login
 */
class LoginViewModelFactory(
    private val appContainer: AppContainer
) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(LoginViewModel::class.java)) {
            return LoginViewModel(appContainer.loginUseCase, appContainer.tokenManager) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
