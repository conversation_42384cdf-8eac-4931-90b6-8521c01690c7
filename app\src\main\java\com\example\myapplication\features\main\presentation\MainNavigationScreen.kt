package com.example.myapplication.features.main.presentation

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.myapplication.core.navigation.BottomNavigationBar
import com.example.myapplication.core.navigation.MainNavigationScreen
import com.example.myapplication.features.dashboard.presentation.DashboardScreen
import com.example.myapplication.features.obras.presentation.ObrasScreen
import com.example.myapplication.features.rankings.presentation.RankingsScreen
import com.example.myapplication.features.profile.presentation.ProfileScreen

/**
 * Tela principal com navegação entre as funcionalidades principais
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar navegação principal
 * Seguindo OCP: Extensível para novas telas principais
 */
@Composable
fun MainNavigationScreen(
    userFullName: String,
    onLogout: () -> Unit,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route ?: MainNavigationScreen.Dashboard.route

    Scaffold(
        modifier = modifier,
        bottomBar = {
            BottomNavigationBar(
                currentRoute = currentRoute,
                onNavigate = { route ->
                    navController.navigate(route) {
                        // Pop up to the start destination of the graph to
                        // avoid building up a large stack of destinations
                        // on the back stack as users select items
                        popUpTo(navController.graph.startDestinationId) {
                            saveState = true
                        }
                        // Avoid multiple copies of the same destination when
                        // reselecting the same item
                        launchSingleTop = true
                        // Restore state when reselecting a previously selected item
                        restoreState = true
                    }
                }
            )
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = MainNavigationScreen.Dashboard.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(MainNavigationScreen.Dashboard.route) {
                DashboardScreen(
                    userFullName = userFullName,
                    onLogout = onLogout
                )
            }
            
            composable(MainNavigationScreen.Obras.route) {
                ObrasScreen()
            }
            
            composable(MainNavigationScreen.Rankings.route) {
                RankingsScreen()
            }
            
            composable(MainNavigationScreen.Profile.route) {
                ProfileScreen(
                    userFullName = userFullName,
                    onLogout = onLogout
                )
            }
        }
    }
}
