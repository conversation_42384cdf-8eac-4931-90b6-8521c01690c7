package com.example.myapplication.features.main.presentation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.myapplication.core.navigation.BottomNavigationBar
import com.example.myapplication.core.navigation.MainNavigationScreen
import com.example.myapplication.features.dashboard.presentation.DashboardScreen
import com.example.myapplication.features.obras.presentation.ObrasScreen
import com.example.myapplication.features.rankings.presentation.RankingsScreen
import com.example.myapplication.features.profile.presentation.ProfileScreen

/**
 * Tela principal com navegação entre as funcionalidades principais
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar navegação principal
 * Seguindo OCP: Extensível para novas telas principais
 */
@Composable
fun MainNavigationScreen(
    userFullName: String,
    onLogout: () -> Unit,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    // DEBUG: Simplificar temporariamente para identificar o problema
    println("DEBUG: MainNavigationScreen iniciando para usuário: $userFullName")

    // DEBUG: Tela super simples para testar
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "🎉 TELA PRINCIPAL FUNCIONANDO! 🎉",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(32.dp))

        Text(
            text = "Bem-vindo, $userFullName!",
            style = MaterialTheme.typography.bodyLarge
        )

        Spacer(modifier = Modifier.height(32.dp))

        Button(
            onClick = onLogout,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Fazer Logout")
        }
    }
}
