{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-52:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83a88cd926a91d6623e89cd1b3365703\\transformed\\material3-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,402,519,618,715,829,971,1089,1228,1313,1415,1507,1605,1723,1845,1952,2094,2238,2370,2546,2672,2793,2913,3032,3125,3225,3348,3486,3585,3691,3797,3941,4086,4193,4292,4375,4470,4564,4675,4760,4844,4945,5025,5108,5207,5307,5402,5504,5591,5695,5794,5899,6030,6110,6214", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "169,285,397,514,613,710,824,966,1084,1223,1308,1410,1502,1600,1718,1840,1947,2089,2233,2365,2541,2667,2788,2908,3027,3120,3220,3343,3481,3580,3686,3792,3936,4081,4188,4287,4370,4465,4559,4670,4755,4839,4940,5020,5103,5202,5302,5397,5499,5586,5690,5789,5894,6025,6105,6209,6304"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1588,1704,1816,1933,2032,2129,2243,2385,2503,2642,2727,2829,2921,3019,3137,3259,3366,3508,3652,3784,3960,4086,4207,4327,4446,4539,4639,4762,4900,4999,5105,5211,5355,5500,5607,5706,5789,5884,5978,6089,6174,6258,6359,6439,6522,6621,6721,6816,6918,7005,7109,7208,7313,7444,7524,7628", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "1583,1699,1811,1928,2027,2124,2238,2380,2498,2637,2722,2824,2916,3014,3132,3254,3361,3503,3647,3779,3955,4081,4202,4322,4441,4534,4634,4757,4895,4994,5100,5206,5350,5495,5602,5701,5784,5879,5973,6084,6169,6253,6354,6434,6517,6616,6716,6811,6913,7000,7104,7203,7308,7439,7519,7623,7718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec212d9d84322c7a85012a35465d0b94\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,504,609,721,8364", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "198,300,401,499,604,716,835,8460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1710ada1ed9a6075fc2f693eee11811c\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,985,1068,1141,1218,1298,1375,1452,1518", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,980,1063,1136,1213,1293,1370,1447,1513,1630"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,932,1018,1110,1206,1289,1375,7723,7810,7891,7974,8057,8130,8207,8287,8465,8542,8608", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "927,1013,1105,1201,1284,1370,1464,7805,7886,7969,8052,8125,8202,8282,8359,8537,8603,8720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb4dfb73410cdab7bbb5bc993c7c0b0\\transformed\\foundation-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8725,8811", "endColumns": "85,88", "endOffsets": "8806,8895"}}]}]}