package com.example.myapplication.features.auth.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.security.TokenManager
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations

class LogoutUseCaseTest {

    @Mock
    private lateinit var tokenManager: TokenManager

    private lateinit var logoutUseCase: LogoutUseCase

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        logoutUseCase = LogoutUseCase(tokenManager)
    }

    @Test
    fun `invoke should clear tokens and return success`() = runTest {
        // When
        val result = logoutUseCase()

        // Then
        verify(tokenManager).clearTokens()
        assertTrue(result is Resource.Success)
    }

    @Test
    fun `invoke should return error when exception occurs`() = runTest {
        // Given
        doThrow(RuntimeException("Test error")).`when`(tokenManager).clearTokens()

        // When
        val result = logoutUseCase()

        // Then
        assertTrue(result is Resource.Error)
        assertEquals("Erro ao fazer logout: Test error", result.message)
    }
}
