package com.example.myapplication.domain.useCase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.constants.AppConstants
import com.example.myapplication.domain.model.RegisterRequest
import com.example.myapplication.domain.model.RegisterResponse
import com.example.myapplication.domain.repository.AuthRepository

/**
 * Caso de uso para registro de novos usuários
 * 
 * Seguindo SRP: Responsabilidade única de executar a lógica de registro
 * Seguindo DIP: Depende da abstração AuthRepository
 */
class RegisterUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(
        username: String,
        email: String,
        password: String,
        fullName: String? = null
    ): Resource<RegisterResponse> {
        // Validações de entrada
        when {
            username.isBlank() -> return Resource.Error("Nome de usuário é obrigatório")
            username.length < AppConstants.Validation.MIN_USERNAME_LENGTH -> 
                return Resource.Error("Nome de usuário deve ter pelo menos ${AppConstants.Validation.MIN_USERNAME_LENGTH} caracteres")
            username.length > 30 -> return Resource.Error("Nome de usuário deve ter no máximo 30 caracteres")
            !username.matches(Regex("^[a-zA-Z0-9_]+$")) -> 
                return Resource.Error("Nome de usuário deve conter apenas letras, números e underscore")
            
            email.isBlank() -> return Resource.Error("Email é obrigatório")
            !isValidEmail(email) -> return Resource.Error("Email deve ser um email válido")
            
            password.isBlank() -> return Resource.Error("Senha é obrigatória")
            password.length < AppConstants.Validation.MIN_PASSWORD_LENGTH -> 
                return Resource.Error("Senha deve ter pelo menos ${AppConstants.Validation.MIN_PASSWORD_LENGTH} caracteres")
            password.length > 100 -> return Resource.Error("Senha deve ter no máximo 100 caracteres")
            
            fullName != null && fullName.isNotBlank() && fullName.length > 100 -> 
                return Resource.Error("Nome completo deve ter no máximo 100 caracteres")
        }

        val registerRequest = RegisterRequest(
            username = username.trim(),
            email = email.trim().lowercase(),
            password = password,
            fullName = fullName?.trim()?.takeIf { it.isNotBlank() }
        )
        
        return authRepository.register(registerRequest)
    }

    /**
     * Valida se o email tem um formato válido
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
