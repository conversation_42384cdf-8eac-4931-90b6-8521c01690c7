package com.example.myapplication.features.auth.presentation.register

import com.example.myapplication.core.common.UiEvent
import com.example.myapplication.domain.model.RegisterResponse

/**
 * Estado da UI da tela de registro
 * 
 * Seguindo SRP: Responsabilidade única de representar o estado da UI de registro
 */
data class RegisterUiState(
    val username: String = "",
    val email: String = "",
    val password: String = "",
    val fullName: String = "",
    val isLoading: Boolean = false,
    val isPasswordVisible: Boolean = false,
    val registerResult: RegisterResult? = null,
    val errorMessage: String? = null
)

/**
 * Resultado do registro
 * 
 * Sealed class para representar diferentes estados do resultado
 */
sealed class RegisterResult {
    data class Success(val registerResponse: RegisterResponse) : RegisterResult()
    data class Error(val message: String) : RegisterResult()
}

/**
 * Eventos da tela de registro
 * 
 * Implementa UiEvent para seguir um padrão consistente
 * Seguindo ISP: Cada evento tem uma responsabilidade específica
 */
sealed class RegisterEvent : UiEvent {
    data class UsernameChanged(val value: String) : RegisterEvent()
    data class EmailChanged(val value: String) : RegisterEvent()
    data class PasswordChanged(val value: String) : RegisterEvent()
    data class FullNameChanged(val value: String) : RegisterEvent()
    object TogglePasswordVisibility : RegisterEvent()
    object Register : RegisterEvent()
    object ClearError : RegisterEvent()
    object NavigateToLogin : RegisterEvent()
}
