package com.example.myapplication.features.profile.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.features.profile.domain.model.UserProfile
import com.example.myapplication.features.profile.domain.repository.ProfileRepository

/**
 * Use case para buscar perfil do usuário
 * 
 * Seguindo SRP: Responsabilidade única de coordenar busca de perfil
 * Seguindo DIP: Depende da abstração ProfileRepository
 */
class GetUserProfileUseCase(
    private val profileRepository: ProfileRepository
) {
    suspend operator fun invoke(): Resource<UserProfile> {
        return profileRepository.getUserProfile()
    }
}
