package com.example.myapplication.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * DTO para resposta de login bem-sucedido
 * Também usado para resposta de registro, pois têm a mesma estrutura
 */
data class LoginResponseDto(
    @SerializedName("access_token")
    val accessToken: String,
    
    @SerializedName("refresh_token")
    val refreshToken: String,
    
    @SerializedName("token_type")
    val tokenType: String,
    
    @SerializedName("expires_in")
    val expiresIn: Long,
    
    @SerializedName("user")
    val user: UserLoginDto?
)

/**
 * DTO para dados do usuário na resposta de login
 */
data class UserLoginDto(
    @SerializedName("id")
    val id: Long,
    
    @SerializedName("username")
    val username: String,
    
    @SerializedName("email")
    val email: String,
    
    @SerializedName("fullName")
    val fullName: String,
    
    @SerializedName("avatar")
    val avatar: String?,
    
    @SerializedName("isActive")
    val isActive: Bo<PERSON>an,
    
    @SerializedName("role")
    val role: String? = null,
    
    @SerializedName("createdAt")
    val createdAt: String,
    
    @SerializedName("updatedAt")
    val updatedAt: String
)

/**
 * DTO para resposta de erro da API
 */
data class ErrorResponseDto(
    @SerializedName("statusCode")
    val statusCode: Int,
    
    @SerializedName("message")
    val message: Any, // Pode ser String ou List<String>
    
    @SerializedName("error")
    val error: String,
    
    @SerializedName("timestamp")
    val timestamp: String,
    
    @SerializedName("path")
    val path: String,
    
    @SerializedName("code")
    val code: String? = null
)
