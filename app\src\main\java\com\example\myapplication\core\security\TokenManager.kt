package com.example.myapplication.core.security

import android.content.Context
import android.content.SharedPreferences
// DEBUG: Comentado temporariamente
// import androidx.security.crypto.EncryptedSharedPreferences
// import androidx.security.crypto.MasterKey
import com.example.myapplication.core.constants.AppConstants
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Gerenciador seguro de tokens JWT
 * 
 * Implementa:
 * - Armazenamento seguro com EncryptedSharedPreferences
 * - Geração de tokens seguros
 * - Observabilidade do estado de autenticação
 * - Limpeza segura de dados
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar tokens
 * Seguindo OCP: Extensível para novos tipos de tokens
 */
class TokenManager private constructor(context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: TokenManager? = null
        
        fun getInstance(context: Context): TokenManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TokenManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    // DEBUG: Usar SharedPreferences normal temporariamente para debug
    private val encryptedPrefs: SharedPreferences = context.getSharedPreferences(
        "debug_prefs",
        Context.MODE_PRIVATE
    )

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()

    private val _currentUser = MutableStateFlow<String?>(null)
    val currentUser: StateFlow<String?> = _currentUser.asStateFlow()

    init {
        // DEBUG: Adicionar logs para debug
        println("DEBUG: TokenManager inicializando...")
        try {
            // Verifica se já existe token válido ao inicializar
            checkAuthenticationStatus()
            println("DEBUG: TokenManager inicializado com sucesso")
        } catch (e: Exception) {
            println("DEBUG: Erro ao inicializar TokenManager: ${e.message}")
            e.printStackTrace()
            // Fallback: definir valores padrão
            _isLoggedIn.value = false
            _currentUser.value = null
        }
    }

    /**
     * Salva os tokens de forma segura
     */
    fun saveTokens(
        accessToken: String,
        refreshToken: String,
        expiresIn: Long,
        userFullName: String
    ) {
        val currentTime = System.currentTimeMillis()
        val expirationTime = currentTime + (expiresIn * 1000) // Convert para milliseconds
        
        encryptedPrefs.edit().apply {
            putString(AppConstants.Preferences.ACCESS_TOKEN, accessToken)
            putString(AppConstants.Preferences.REFRESH_TOKEN, refreshToken)
            putLong("token_expiration", expirationTime)
            putString("user_full_name", userFullName)
            putBoolean(AppConstants.Preferences.IS_LOGGED_IN, true)
            apply()
        }
        
        _isLoggedIn.value = true
        _currentUser.value = userFullName
    }

    /**
     * Obtém o token de acesso se ainda for válido
     */
    fun getAccessToken(): String? {
        val token = encryptedPrefs.getString(AppConstants.Preferences.ACCESS_TOKEN, null)
        val expirationTime = encryptedPrefs.getLong("token_expiration", 0)
        
        return if (token != null && System.currentTimeMillis() < expirationTime) {
            token
        } else {
            null
        }
    }

    /**
     * Obtém o refresh token
     */
    fun getRefreshToken(): String? {
        return encryptedPrefs.getString(AppConstants.Preferences.REFRESH_TOKEN, null)
    }

    /**
     * Verifica se o token está próximo do vencimento (dentro de 5 minutos)
     */
    fun isTokenExpiringSoon(): Boolean {
        val expirationTime = encryptedPrefs.getLong("token_expiration", 0)
        val fiveMinutesInMillis = 5 * 60 * 1000 // 5 minutos
        return System.currentTimeMillis() + fiveMinutesInMillis >= expirationTime
    }

    /**
     * Verifica se o usuário está autenticado
     */
    fun isAuthenticated(): Boolean {
        return getAccessToken() != null
    }

    /**
     * Obtém o nome do usuário atual
     */
    fun getCurrentUserName(): String? {
        return encryptedPrefs.getString("user_full_name", null)
    }

    /**
     * Limpa todos os dados de autenticação de forma segura
     */
    fun clearTokens() {
        encryptedPrefs.edit().apply {
            remove(AppConstants.Preferences.ACCESS_TOKEN)
            remove(AppConstants.Preferences.REFRESH_TOKEN)
            remove("token_expiration")
            remove("user_full_name")
            putBoolean(AppConstants.Preferences.IS_LOGGED_IN, false)
            apply()
        }
        
        _isLoggedIn.value = false
        _currentUser.value = null
    }

    /**
     * Atualiza o access token após renovação
     */
    fun updateAccessToken(newAccessToken: String, expiresIn: Long) {
        val currentTime = System.currentTimeMillis()
        val expirationTime = currentTime + (expiresIn * 1000)
        
        encryptedPrefs.edit().apply {
            putString(AppConstants.Preferences.ACCESS_TOKEN, newAccessToken)
            putLong("token_expiration", expirationTime)
            apply()
        }
    }

    /**
     * Verifica status de autenticação ao inicializar
     */
    private fun checkAuthenticationStatus() {
        val isLoggedIn = encryptedPrefs.getBoolean(AppConstants.Preferences.IS_LOGGED_IN, false)
        val userFullName = encryptedPrefs.getString("user_full_name", null)
        
        if (isLoggedIn && isAuthenticated()) {
            _isLoggedIn.value = true
            _currentUser.value = userFullName
        } else {
            // Token expirado, limpa dados
            clearTokens()
        }
    }
}
