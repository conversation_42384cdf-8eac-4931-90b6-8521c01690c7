package com.example.myapplication.core.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * Componente de Bottom Navigation
 * 
 * Seguindo SRP: Responsabilidade única de gerenciar navegação inferior
 * Seguindo OCP: Extensível para novos itens de navegação
 */
@Composable
fun BottomNavigationBar(
    currentRoute: String,
    onNavigate: (String) -> Unit
) {
    NavigationBar {
        bottomNavigationItems.forEach { item ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = item.icon,
                        contentDescription = item.title
                    )
                },
                label = { Text(item.title) },
                selected = currentRoute == item.route,
                onClick = { onNavigate(item.route) }
            )
        }
    }
}

/**
 * Item de navegação do Bottom Navigation
 */
data class BottomNavigationItem(
    val route: String,
    val title: String,
    val icon: ImageVector
)

/**
 * Lista de itens do Bottom Navigation
 */
val bottomNavigationItems = listOf(
    BottomNavigationItem(
        route = MainNavigationScreen.Dashboard.route,
        title = "Dashboard",
        icon = Icons.Default.Dashboard
    ),
    BottomNavigationItem(
        route = MainNavigationScreen.Obras.route,
        title = "Obras",
        icon = Icons.Default.Build
    ),
    BottomNavigationItem(
        route = MainNavigationScreen.Rankings.route,
        title = "Rankings",
        icon = Icons.Default.EmojiEvents
    ),
    BottomNavigationItem(
        route = MainNavigationScreen.Profile.route,
        title = "Perfil",
        icon = Icons.Default.Person
    )
)

/**
 * Telas de navegação principal (após login)
 */
sealed class MainNavigationScreen(val route: String) {
    object Dashboard : MainNavigationScreen("dashboard")
    object Obras : MainNavigationScreen("obras")
    object Rankings : MainNavigationScreen("rankings")
    object Profile : MainNavigationScreen("profile")
}
