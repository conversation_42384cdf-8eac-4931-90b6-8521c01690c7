{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-51:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\83a88cd926a91d6623e89cd1b3365703\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4864,4951,5039,5149,5229,5314,5409,5512,5603,5702,5791,5899,5999,6105,6223,6303,6407", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4859,4946,5034,5144,5224,5309,5404,5507,5598,5697,5786,5894,5994,6100,6218,6298,6402,6497"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1468,1615,1762,1889,2034,2163,2261,2376,2516,2635,2780,2864,2969,3065,3165,3284,3405,3515,3658,3802,3937,4128,4253,4375,4499,4621,4718,4815,4943,5078,5176,5279,5385,5532,5683,5791,5891,5967,6063,6158,6277,6364,6452,6562,6642,6727,6822,6925,7016,7115,7204,7312,7412,7518,7636,7716,7820", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "1610,1757,1884,2029,2158,2256,2371,2511,2630,2775,2859,2964,3060,3160,3279,3400,3510,3653,3797,3932,4123,4248,4370,4494,4616,4713,4810,4938,5073,5171,5274,5380,5527,5678,5786,5886,5962,6058,6153,6272,6359,6447,6557,6637,6722,6817,6920,7011,7110,7199,7307,7407,7513,7631,7711,7815,7910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1710ada1ed9a6075fc2f693eee11811c\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,998,1088,1162,1239,1321,1399,1476,1544", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,993,1083,1157,1234,1316,1394,1471,1539,1659"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,927,1008,1109,1213,1305,1381,7915,8004,8088,8176,8266,8340,8417,8499,8678,8755,8823", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "922,1003,1104,1208,1300,1376,1463,7999,8083,8171,8261,8335,8412,8494,8572,8750,8818,8938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ec212d9d84322c7a85012a35465d0b94\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,406,509,614,717,8577", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "198,301,401,504,609,712,831,8673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4eb4dfb73410cdab7bbb5bc993c7c0b0\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8943,9029", "endColumns": "85,88", "endOffsets": "9024,9113"}}]}]}