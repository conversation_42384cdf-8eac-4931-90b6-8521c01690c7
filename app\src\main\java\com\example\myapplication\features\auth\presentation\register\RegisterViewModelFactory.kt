package com.example.myapplication.features.auth.presentation.register

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.core.di.AppContainer

/**
 * Factory para criar instâncias do RegisterViewModel
 * 
 * Seguindo DIP: Usa o AppContainer para resolver dependências
 * Seguindo SRP: Responsabilidade única de criar ViewModels de registro
 */
class RegisterViewModelFactory : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(RegisterViewModel::class.java)) {
            return RegisterViewModel(AppContainer.registerUseCase) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
