package com.example.myapplication.core.constants

/**
 * Constantes globais da aplicação
 * 
 * Seguindo SRP: Esta classe tem a responsabilidade única de definir constantes
 */
object AppConstants {
    
    // URLs da API
    object Api {
        const val BASE_URL = "http://10.0.2.2:3000/"
        const val TIMEOUT = 30L // segundos
    }
    
    // Navegação
    object Routes {
        const val LOGIN = "login"
        const val REGISTER = "register"
        const val MAIN = "main"
        const val NETWORK_TEST = "network_test"
    }
    
    // Preferências
    object Preferences {
        const val USER_PREFERENCES = "user_preferences"
        const val IS_LOGGED_IN = "is_logged_in"
        const val ACCESS_TOKEN = "access_token"
        const val REFRESH_TOKEN = "refresh_token"
    }
    
    // Validação
    object Validation {
        const val MIN_PASSWORD_LENGTH = 6
        const val MIN_USERNAME_LENGTH = 3
    }
}
