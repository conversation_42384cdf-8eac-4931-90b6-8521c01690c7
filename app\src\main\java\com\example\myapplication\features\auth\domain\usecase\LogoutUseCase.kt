package com.example.myapplication.features.auth.domain.usecase

import com.example.myapplication.core.common.Resource
import com.example.myapplication.core.security.TokenManager

/**
 * Use case para logout com limpeza segura de dados
 * 
 * Implementa:
 * - Limpeza segura de todos os tokens
 * - Invalidação de sessão local
 * - Reset do estado de autenticação
 * 
 * Seguindo SRP: Responsabilidade única de realizar logout
 * Seguindo DIP: Depende da abstração TokenManager
 */
class LogoutUseCase(
    private val tokenManager: TokenManager
) {
    suspend operator fun invoke(): Resource<Unit> {
        return try {
            // Limpa todos os dados de autenticação
            tokenManager.clearTokens()
            
            // TODO: Se a API tiver endpoint de logout, chamar aqui
            // para invalidar o token no servidor também
            
            Resource.Success(Unit)
        } catch (e: Exception) {
            Resource.Error("Erro ao fazer logout: ${e.message}")
        }
    }
}
