package com.example.myapplication.core.common

/**
 * Classes e tipos utilitários comuns
 * 
 * Seguindo SRP: Cada sealed class tem uma responsabilidade específica
 */

/**
 * Wrapper para recursos que podem estar em estados de loading, sucesso ou erro
 * 
 * @param T Tipo dos dados
 */
sealed class Resource<T>(
    val data: T? = null,
    val message: String? = null
) {
    class Success<T>(data: T) : Resource<T>(data)
    class Error<T>(message: String, data: T? = null) : Resource<T>(data, message)
    class Loading<T>(data: T? = null) : Resource<T>(data)
}

/**
 * Estados da UI comum para carregamento
 */
data class UiState<T>(
    val isLoading: Boolean = false,
    val data: T? = null,
    val error: String? = null
)

/**
 * Interface para eventos da UI
 */
interface UiEvent

/**
 * Interface para efeitos da UI (navegação, toasts, etc.)
 */
interface UiEffect
