package com.example.myapplication.domain.repository

import com.example.myapplication.core.common.Resource
import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.RegisterRequest
import com.example.myapplication.domain.model.RegisterResponse

/**
 * Interface do repositório de autenticação
 * 
 * Seguindo DIP: Abstração que define contratos para autenticação
 * Seguindo ISP: Interface específica para operações de autenticação
 */
interface AuthRepository {
    suspend fun login(loginRequest: LoginRequest): Resource<LoginResponse>
    suspend fun register(registerRequest: RegisterRequest): Resource<RegisterResponse>
}
