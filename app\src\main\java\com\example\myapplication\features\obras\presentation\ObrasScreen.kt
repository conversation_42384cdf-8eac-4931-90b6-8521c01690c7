package com.example.myapplication.features.obras.presentation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.ui.theme.MyApplicationTheme

/**
 * Tela de Obras - Gerenciamento de obras
 * 
 * Seguindo SRP: Responsabilidade única de exibir e gerenciar obras
 * Seguindo OCP: Extensível para novas funcionalidades de obras
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ObrasScreen(
    modifier: Modifier = Modifier
) {
    var selectedFilter by remember { mutableStateOf(ObraFilter.TODAS) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        ObrasHeader()
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Filtros
        FilterSection(
            selectedFilter = selectedFilter,
            onFilterSelected = { selectedFilter = it }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Lista de obras
        ObrasListSection(filter = selectedFilter)
    }
}

@Composable
private fun ObrasHeader() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = "Obras",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "Gerencie suas obras",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        FloatingActionButton(
            onClick = { /* TODO: Implementar nova obra */ },
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "Nova obra"
            )
        }
    }
}

@Composable
private fun FilterSection(
    selectedFilter: ObraFilter,
    onFilterSelected: (ObraFilter) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(ObraFilter.values().size) { index ->
            val filter = ObraFilter.values()[index]
            FilterChip(
                selected = selectedFilter == filter,
                onClick = { onFilterSelected(filter) },
                label = { Text(filter.displayName) },
                leadingIcon = if (selectedFilter == filter) {
                    {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                } else null
            )
        }
    }
}

@Composable
private fun ObrasListSection(filter: ObraFilter) {
    val obras = remember { getSampleObras().filter { obra ->
        when (filter) {
            ObraFilter.TODAS -> true
            ObraFilter.EM_ANDAMENTO -> obra.status == ObraStatus.EM_ANDAMENTO
            ObraFilter.CONCLUIDAS -> obra.status == ObraStatus.CONCLUIDA
            ObraFilter.PAUSADAS -> obra.status == ObraStatus.PAUSADA
        }
    }}
    
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(obras) { obra ->
            ObraCard(obra = obra)
        }
        
        if (obras.isEmpty()) {
            item {
                EmptyStateCard(filter = filter)
            }
        }
    }
}

@Composable
private fun ObraCard(obra: Obra) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = { /* TODO: Navegar para detalhes da obra */ }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = obra.nome,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = obra.endereco,
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
                
                StatusChip(status = obra.status)
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Progresso
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Progresso",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "${(obra.progresso * 100).toInt()}%",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                LinearProgressIndicator(
                    progress = obra.progresso,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Informações adicionais
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                InfoItem(
                    icon = Icons.Default.CalendarToday,
                    text = obra.dataInicio
                )
                InfoItem(
                    icon = Icons.Default.Person,
                    text = obra.responsavel
                )
            }
        }
    }
}

@Composable
private fun StatusChip(status: ObraStatus) {
    val (color, text) = when (status) {
        ObraStatus.EM_ANDAMENTO -> MaterialTheme.colorScheme.primary to "Em Andamento"
        ObraStatus.CONCLUIDA -> MaterialTheme.colorScheme.tertiary to "Concluída"
        ObraStatus.PAUSADA -> MaterialTheme.colorScheme.error to "Pausada"
    }
    
    AssistChip(
        onClick = { },
        label = { Text(text, fontSize = 12.sp) },
        colors = AssistChipDefaults.assistChipColors(
            containerColor = color.copy(alpha = 0.1f),
            labelColor = color
        )
    )
}

@Composable
private fun InfoItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(16.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun EmptyStateCard(filter: ObraFilter) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Build,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = when (filter) {
                    ObraFilter.TODAS -> "Nenhuma obra encontrada"
                    ObraFilter.EM_ANDAMENTO -> "Nenhuma obra em andamento"
                    ObraFilter.CONCLUIDAS -> "Nenhuma obra concluída"
                    ObraFilter.PAUSADAS -> "Nenhuma obra pausada"
                },
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "Adicione uma nova obra para começar",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

// Data classes e enums
data class Obra(
    val id: String,
    val nome: String,
    val endereco: String,
    val status: ObraStatus,
    val progresso: Float,
    val dataInicio: String,
    val responsavel: String
)

enum class ObraStatus {
    EM_ANDAMENTO,
    CONCLUIDA,
    PAUSADA
}

enum class ObraFilter(val displayName: String) {
    TODAS("Todas"),
    EM_ANDAMENTO("Em Andamento"),
    CONCLUIDAS("Concluídas"),
    PAUSADAS("Pausadas")
}

// Sample data
private fun getSampleObras(): List<Obra> = listOf(
    Obra(
        id = "1",
        nome = "Casa Silva",
        endereco = "Rua das Flores, 123",
        status = ObraStatus.EM_ANDAMENTO,
        progresso = 0.65f,
        dataInicio = "15/01/2024",
        responsavel = "João Santos"
    ),
    Obra(
        id = "2",
        nome = "Edifício Central",
        endereco = "Av. Principal, 456",
        status = ObraStatus.EM_ANDAMENTO,
        progresso = 0.30f,
        dataInicio = "20/02/2024",
        responsavel = "Maria Costa"
    ),
    Obra(
        id = "3",
        nome = "Reforma Escritório",
        endereco = "Rua Comercial, 789",
        status = ObraStatus.CONCLUIDA,
        progresso = 1.0f,
        dataInicio = "10/12/2023",
        responsavel = "Pedro Lima"
    ),
    Obra(
        id = "4",
        nome = "Casa Moderna",
        endereco = "Rua Nova, 321",
        status = ObraStatus.PAUSADA,
        progresso = 0.45f,
        dataInicio = "05/03/2024",
        responsavel = "Ana Silva"
    )
)

@Preview(showBackground = true)
@Composable
fun ObrasScreenPreview() {
    MyApplicationTheme {
        ObrasScreen()
    }
}
