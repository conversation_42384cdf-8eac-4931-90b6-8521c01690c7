package com.example.myapplication.features.rankings.presentation

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapplication.ui.theme.MyApplicationTheme

/**
 * Tela de Rankings - Visualização de rankings e estatísticas
 * 
 * Seguindo SRP: Responsabilidade única de exibir rankings
 * Seguindo OCP: Extensível para novos tipos de rankings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RankingsScreen(
    modifier: Modifier = Modifier
) {
    var selectedCategory by remember { mutableStateOf(RankingCategory.OBRAS_CONCLUIDAS) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        RankingsHeader()
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Categorias
        CategorySection(
            selectedCategory = selectedCategory,
            onCategorySelected = { selectedCategory = it }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Minha posição
        MyPositionCard(category = selectedCategory)
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Lista de rankings
        RankingListSection(category = selectedCategory)
    }
}

@Composable
private fun RankingsHeader() {
    Column {
        Text(
            text = "Rankings",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = "Veja sua posição e compare com outros",
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun CategorySection(
    selectedCategory: RankingCategory,
    onCategorySelected: (RankingCategory) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(RankingCategory.values().size) { index ->
            val category = RankingCategory.values()[index]
            FilterChip(
                selected = selectedCategory == category,
                onClick = { onCategorySelected(category) },
                label = { Text(category.displayName) },
                leadingIcon = if (selectedCategory == category) {
                    {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                } else null
            )
        }
    }
}

@Composable
private fun MyPositionCard(category: RankingCategory) {
    val myPosition = getCurrentUserPosition(category)
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Sua Posição",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "#${myPosition.posicao}",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = myPosition.nome,
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Icon(
                    imageVector = getPositionIcon(myPosition.posicao),
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "${myPosition.pontos} pts",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

@Composable
private fun RankingListSection(category: RankingCategory) {
    val rankings = remember(category) { getSampleRankings(category) }
    
    Text(
        text = "Ranking Geral",
        fontSize = 20.sp,
        fontWeight = FontWeight.SemiBold,
        modifier = Modifier.padding(bottom = 12.dp)
    )
    
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        itemsIndexed(rankings) { index, ranking ->
            RankingItem(
                ranking = ranking,
                position = index + 1,
                isCurrentUser = ranking.isCurrentUser
            )
        }
    }
}

@Composable
private fun RankingItem(
    ranking: RankingEntry,
    position: Int,
    isCurrentUser: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = if (isCurrentUser) {
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.secondaryContainer
            )
        } else {
            CardDefaults.cardColors()
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Posição
            Box(
                modifier = Modifier.size(40.dp),
                contentAlignment = Alignment.Center
            ) {
                if (position <= 3) {
                    Icon(
                        imageVector = getPositionIcon(position),
                        contentDescription = "Posição $position",
                        modifier = Modifier.size(32.dp),
                        tint = getPositionColor(position)
                    )
                } else {
                    Text(
                        text = "#$position",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Informações do usuário
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = ranking.nome,
                    fontSize = 16.sp,
                    fontWeight = if (isCurrentUser) FontWeight.Bold else FontWeight.Medium
                )
                Text(
                    text = ranking.empresa,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Pontuação
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${ranking.pontos}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "pontos",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

private fun getPositionIcon(position: Int) = when (position) {
    1 -> Icons.Default.EmojiEvents
    2 -> Icons.Default.WorkspacePremium
    3 -> Icons.Default.Star
    else -> Icons.Default.Person
}

@Composable
private fun getPositionColor(position: Int) = when (position) {
    1 -> MaterialTheme.colorScheme.tertiary // Ouro
    2 -> MaterialTheme.colorScheme.secondary // Prata
    3 -> MaterialTheme.colorScheme.primary // Bronze
    else -> MaterialTheme.colorScheme.onSurface
}

// Data classes e enums
data class RankingEntry(
    val id: String,
    val nome: String,
    val empresa: String,
    val pontos: Int,
    val posicao: Int = 0,
    val isCurrentUser: Boolean = false
)

enum class RankingCategory(val displayName: String) {
    OBRAS_CONCLUIDAS("Obras Concluídas"),
    QUALIDADE("Qualidade"),
    PRAZO("Cumprimento de Prazo"),
    SATISFACAO("Satisfação do Cliente")
}

// Sample data functions
private fun getCurrentUserPosition(category: RankingCategory): RankingEntry {
    return when (category) {
        RankingCategory.OBRAS_CONCLUIDAS -> RankingEntry(
            id = "current",
            nome = "Você (João Silva)",
            empresa = "Silva Construções",
            pontos = 1250,
            posicao = 3,
            isCurrentUser = true
        )
        RankingCategory.QUALIDADE -> RankingEntry(
            id = "current",
            nome = "Você (João Silva)",
            empresa = "Silva Construções",
            pontos = 950,
            posicao = 5,
            isCurrentUser = true
        )
        RankingCategory.PRAZO -> RankingEntry(
            id = "current",
            nome = "Você (João Silva)",
            empresa = "Silva Construções",
            pontos = 1100,
            posicao = 2,
            isCurrentUser = true
        )
        RankingCategory.SATISFACAO -> RankingEntry(
            id = "current",
            nome = "Você (João Silva)",
            empresa = "Silva Construções",
            pontos = 1300,
            posicao = 1,
            isCurrentUser = true
        )
    }
}



private fun getSampleRankings(category: RankingCategory): List<RankingEntry> {
    return when (category) {
        RankingCategory.OBRAS_CONCLUIDAS -> listOf(
            RankingEntry("1", "Maria Santos", "Santos & Cia", 1500, 1),
            RankingEntry("2", "Pedro Costa", "Costa Engenharia", 1350, 2),
            RankingEntry("current", "Você (João Silva)", "Silva Construções", 1250, 3, true),
            RankingEntry("4", "Ana Oliveira", "Oliveira Obras", 1200, 4),
            RankingEntry("5", "Carlos Lima", "Lima Construções", 1150, 5)
        )
        RankingCategory.QUALIDADE -> listOf(
            RankingEntry("1", "Ana Oliveira", "Oliveira Obras", 1400, 1),
            RankingEntry("2", "Carlos Lima", "Lima Construções", 1300, 2),
            RankingEntry("3", "Maria Santos", "Santos & Cia", 1200, 3),
            RankingEntry("4", "Pedro Costa", "Costa Engenharia", 1000, 4),
            RankingEntry("current", "Você (João Silva)", "Silva Construções", 950, 5, true)
        )
        RankingCategory.PRAZO -> listOf(
            RankingEntry("1", "Pedro Costa", "Costa Engenharia", 1450, 1),
            RankingEntry("current", "Você (João Silva)", "Silva Construções", 1100, 2, true),
            RankingEntry("3", "Maria Santos", "Santos & Cia", 1050, 3),
            RankingEntry("4", "Ana Oliveira", "Oliveira Obras", 900, 4),
            RankingEntry("5", "Carlos Lima", "Lima Construções", 850, 5)
        )
        RankingCategory.SATISFACAO -> listOf(
            RankingEntry("current", "Você (João Silva)", "Silva Construções", 1300, 1, true),
            RankingEntry("2", "Ana Oliveira", "Oliveira Obras", 1250, 2),
            RankingEntry("3", "Maria Santos", "Santos & Cia", 1200, 3),
            RankingEntry("4", "Pedro Costa", "Costa Engenharia", 1100, 4),
            RankingEntry("5", "Carlos Lima", "Lima Construções", 1000, 5)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RankingsScreenPreview() {
    MyApplicationTheme {
        RankingsScreen()
    }
}
