package com.example.myapplication.features.profile.domain.model

/**
 * Domain model para perfil do usuário
 * 
 * Seguindo SRP: Responsabilidade única de representar dados do perfil
 * Seguindo OCP: Extensível para novos campos sem modificar existentes
 */
data class UserProfile(
    val id: Long,
    val username: String,
    val email: String,
    val fullName: String,
    val avatar: String?,
    val isActive: Boolean,
    val role: String,
    val createdAt: String,
    val updatedAt: String,
    val stats: UserStats
)

/**
 * Domain model para estatísticas do usuário
 */
data class UserStats(
    val readingStats: ReadingStats,
    val totalChaptersRead: Int,
    val totalLists: Int,
    val totalPublicLists: Int,
    val totalRankings: Int,
    val totalPublicRankings: Int,
    val totalReviews: Int,
    val totalPublicReviews: Int,
    val averageRating: Double,
    val totalReviewLikes: Int,
    val averageReadingTime: Double,
    val lastReadingActivity: String,
    val favoriteGenre: String
)

/**
 * Domain model para estatísticas de leitura
 */
data class ReadingStats(
    val reading: Int,
    val completed: Int,
    val dropped: Int,
    val planToRead: Int,
    val onHold: Int,
    val totalWorks: Int
)
